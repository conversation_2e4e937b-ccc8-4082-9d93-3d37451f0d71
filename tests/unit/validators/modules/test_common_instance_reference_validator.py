"""
Test CommonInstanceReferenceValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.12.2 Common Instance Reference Module validation.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.common_instance_reference_validator import CommonInstanceReferenceValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult


class TestCommonInstanceReferenceValidator:
    """Test CommonInstanceReferenceValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_passes_validation(self):
        """Test that empty dataset passes validation (all elements are Type 1C conditional)."""
        dataset = Dataset()
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_validation_config_default_behavior(self):
        """Test that validation uses default config when none provided."""
        dataset = Dataset()
        
        result = CommonInstanceReferenceValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
    
    def test_validation_config_parameters_respected(self):
        """Test that ValidationConfig parameters control validation behavior."""
        # Create dataset with invalid sequence structure
        dataset = Dataset()
        dataset.ReferencedSeriesSequence = [Dataset()]  # Missing required elements
        
        # Test with sequences validation disabled
        config_no_sequences = ValidationConfig(validate_sequences=False)
        result = CommonInstanceReferenceValidator.validate(dataset, config_no_sequences)
        assert len(result.errors) == 0  # Should skip sequence validation
        
        # Test with sequences validation enabled (default)
        config_with_sequences = ValidationConfig(validate_sequences=True)
        result = CommonInstanceReferenceValidator.validate(dataset, config_with_sequences)
        assert len(result.errors) > 0  # Should detect sequence validation errors


class TestConditionalRequirementsValidation:
    """Test Type 1C conditional requirements validation."""
    
    def test_no_sequences_is_valid(self):
        """Test that having no sequences is valid (no references)."""
        dataset = Dataset()
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_only_referenced_series_is_valid(self):
        """Test that having only Referenced Series Sequence is valid."""
        dataset = Dataset()
        dataset.ReferencedSeriesSequence = [self._create_valid_referenced_series_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should pass conditional requirements (structure errors may exist but not conditional errors)
        conditional_errors = [error for error in result.errors if 'Type 1C' in error]
        assert len(conditional_errors) == 0
    
    def test_only_other_studies_is_valid(self):
        """Test that having only Studies Containing Other Referenced Instances Sequence is valid."""
        dataset = Dataset()
        dataset.StudiesContainingOtherReferencedInstancesSequence = [self._create_valid_other_study_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should pass conditional requirements (structure errors may exist but not conditional errors)
        conditional_errors = [error for error in result.errors if 'Type 1C' in error]
        assert len(conditional_errors) == 0
    
    def test_both_sequences_is_valid(self):
        """Test that having both sequences is valid."""
        dataset = Dataset()
        dataset.ReferencedSeriesSequence = [self._create_valid_referenced_series_item()]
        dataset.StudiesContainingOtherReferencedInstancesSequence = [self._create_valid_other_study_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should pass conditional requirements (structure errors may exist but not conditional errors)
        conditional_errors = [error for error in result.errors if 'Type 1C' in error]
        assert len(conditional_errors) == 0
    
    def _create_valid_referenced_series_item(self) -> Dataset:
        """Create a valid referenced series item for testing."""
        item = Dataset()
        item.SeriesInstanceUID = "*******.*******.9"
        item.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        return item
    
    def _create_valid_other_study_item(self) -> Dataset:
        """Create a valid other study item for testing."""
        item = Dataset()
        item.StudyInstanceUID = "*******.*******.11"
        return item
    
    def _create_valid_referenced_instance_item(self) -> Dataset:
        """Create a valid referenced instance item for testing."""
        item = Dataset()
        item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        item.ReferencedSOPInstanceUID = "*******.*******.10"
        return item


class TestSequenceStructureValidation:
    """Test sequence structure validation for Required Series Sequence."""
    
    def test_referenced_series_missing_series_instance_uid(self):
        """Test validation error when Referenced Series Sequence item missing Series Instance UID."""
        dataset = Dataset()
        incomplete_item = Dataset()
        # Missing SeriesInstanceUID
        incomplete_item.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        dataset.ReferencedSeriesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        series_uid_errors = [error for error in result.errors if 'Series Instance UID (0020,000E) is required' in error]
        assert len(series_uid_errors) == 1
        assert 'Type 1' in series_uid_errors[0]
    
    def test_referenced_series_missing_referenced_instance_sequence(self):
        """Test validation error when Referenced Series Sequence item missing Referenced Instance Sequence."""
        dataset = Dataset()
        incomplete_item = Dataset()
        incomplete_item.SeriesInstanceUID = "*******.*******.9"
        # Missing ReferencedInstanceSequence
        dataset.ReferencedSeriesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        instance_seq_errors = [error for error in result.errors if 'Referenced Instance Sequence (0008,114A) is required' in error]
        assert len(instance_seq_errors) == 1
        assert 'Type 1' in instance_seq_errors[0]
    
    def test_referenced_series_empty_referenced_instance_sequence(self):
        """Test validation warning when Referenced Instance Sequence is empty."""
        dataset = Dataset()
        item_with_empty_seq = Dataset()
        item_with_empty_seq.SeriesInstanceUID = "*******.*******.9"
        item_with_empty_seq.ReferencedInstanceSequence = []  # Empty sequence
        dataset.ReferencedSeriesSequence = [item_with_empty_seq]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.warnings) > 0
        empty_seq_warnings = [warning for warning in result.warnings if 'Referenced Instance Sequence (0008,114A) is empty' in warning]
        assert len(empty_seq_warnings) == 1
    
    def test_valid_referenced_series_structure(self):
        """Test that valid Referenced Series Sequence structure passes validation."""
        dataset = Dataset()
        dataset.ReferencedSeriesSequence = [self._create_valid_referenced_series_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should have no structure errors for this sequence
        series_errors = [error for error in result.errors if 'Referenced Series Sequence' in error and 'Series Instance UID' in error]
        instance_errors = [error for error in result.errors if 'Referenced Instance Sequence' in error and 'required' in error]
        assert len(series_errors) == 0
        assert len(instance_errors) == 0
    
    def _create_valid_referenced_series_item(self) -> Dataset:
        """Create a valid referenced series item for testing."""
        item = Dataset()
        item.SeriesInstanceUID = "*******.*******.9"
        item.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        return item
    
    def _create_valid_referenced_instance_item(self) -> Dataset:
        """Create a valid referenced instance item for testing."""
        item = Dataset()
        item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        item.ReferencedSOPInstanceUID = "*******.*******.10"
        return item


class TestOtherStudiesValidation:
    """Test Studies Containing Other Referenced Instances Sequence validation."""
    
    def test_other_studies_missing_study_instance_uid(self):
        """Test validation error when other studies item missing Study Instance UID."""
        dataset = Dataset()
        incomplete_item = Dataset()
        # Missing StudyInstanceUID
        dataset.StudiesContainingOtherReferencedInstancesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        study_uid_errors = [error for error in result.errors if 'Study Instance UID (0020,000D) is required' in error]
        assert len(study_uid_errors) == 1
        assert 'Type 1' in study_uid_errors[0]
    
    def test_other_studies_with_series_reference_macro(self):
        """Test validation of Series and Instance Reference Macro within other studies."""
        dataset = Dataset()
        study_item = Dataset()
        study_item.StudyInstanceUID = "*******.*******.11"
        
        # Add Series and Instance Reference Macro
        incomplete_series_item = Dataset()
        # Missing SeriesInstanceUID
        study_item.ReferencedSeriesSequence = [incomplete_series_item]
        
        dataset.StudiesContainingOtherReferencedInstancesSequence = [study_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        series_errors = [error for error in result.errors if 'Studies Containing Other Referenced Instances Sequence' in error and 'Series Instance UID' in error]
        assert len(series_errors) == 1
    
    def test_valid_other_studies_structure(self):
        """Test that valid other studies structure passes validation."""
        dataset = Dataset()
        dataset.StudiesContainingOtherReferencedInstancesSequence = [self._create_valid_other_study_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should have no structure errors for this sequence
        study_errors = [error for error in result.errors if 'Studies Containing Other Referenced Instances' in error and 'Study Instance UID' in error]
        assert len(study_errors) == 0
    
    def _create_valid_other_study_item(self) -> Dataset:
        """Create a valid other study item for testing."""
        item = Dataset()
        item.StudyInstanceUID = "*******.*******.11"
        return item


class TestSOPInstanceReferenceMacroValidation:
    """Test SOP Instance Reference Macro (Table 10-11) validation."""
    
    def test_referenced_instance_missing_sop_class_uid(self):
        """Test validation error when referenced instance missing SOP Class UID."""
        dataset = Dataset()
        series_item = Dataset()
        series_item.SeriesInstanceUID = "*******.*******.9"
        
        incomplete_instance = Dataset()
        # Missing ReferencedSOPClassUID
        incomplete_instance.ReferencedSOPInstanceUID = "*******.*******.10"
        series_item.ReferencedInstanceSequence = [incomplete_instance]
        
        dataset.ReferencedSeriesSequence = [series_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        sop_class_errors = [error for error in result.errors if 'Referenced SOP Class UID (0008,1150) is required (Type 1)' in error]
        assert len(sop_class_errors) == 1
    
    def test_referenced_instance_missing_sop_instance_uid(self):
        """Test validation error when referenced instance missing SOP Instance UID."""
        dataset = Dataset()
        series_item = Dataset()
        series_item.SeriesInstanceUID = "*******.*******.9"
        
        incomplete_instance = Dataset()
        incomplete_instance.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        # Missing ReferencedSOPInstanceUID
        series_item.ReferencedInstanceSequence = [incomplete_instance]
        
        dataset.ReferencedSeriesSequence = [series_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        sop_instance_errors = [error for error in result.errors if 'Referenced SOP Instance UID (0008,1155) is required (Type 1)' in error]
        assert len(sop_instance_errors) == 1
    
    def test_referenced_instance_in_other_studies_validation(self):
        """Test SOP Instance Reference Macro validation within other studies sequence."""
        dataset = Dataset()
        study_item = Dataset()
        study_item.StudyInstanceUID = "*******.*******.11"
        
        series_item = Dataset()
        series_item.SeriesInstanceUID = "*******.*******.9"
        
        incomplete_instance = Dataset()
        # Missing both required SOP UIDs
        series_item.ReferencedInstanceSequence = [incomplete_instance]
        study_item.ReferencedSeriesSequence = [series_item]
        
        dataset.StudiesContainingOtherReferencedInstancesSequence = [study_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) >= 2  # Should have both SOP UID errors
        sop_class_errors = [error for error in result.errors if 'Referenced SOP Class UID (0008,1150) is required (Type 1)' in error]
        sop_instance_errors = [error for error in result.errors if 'Referenced SOP Instance UID (0008,1155) is required (Type 1)' in error]
        assert len(sop_class_errors) == 1
        assert len(sop_instance_errors) == 1
    
    def test_valid_sop_instance_reference_macro(self):
        """Test that valid SOP Instance Reference Macro passes validation."""
        dataset = Dataset()
        dataset.ReferencedSeriesSequence = [self._create_valid_referenced_series_item()]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should have no SOP Instance Reference Macro errors
        sop_errors = [error for error in result.errors if 'Referenced SOP' in error and 'UID' in error and 'required' in error]
        assert len(sop_errors) == 0
    
    def _create_valid_referenced_series_item(self) -> Dataset:
        """Create a valid referenced series item for testing."""
        item = Dataset()
        item.SeriesInstanceUID = "*******.*******.9"
        item.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        return item
    
    def _create_valid_referenced_instance_item(self) -> Dataset:
        """Create a valid referenced instance item for testing."""
        item = Dataset()
        item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        item.ReferencedSOPInstanceUID = "*******.*******.10"
        return item


class TestErrorMessageQuality:
    """Test quality and specificity of validation error messages."""
    
    def test_error_messages_include_dicom_tags(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        incomplete_item = Dataset()
        # Missing required elements
        dataset.ReferencedSeriesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        assert len(result.errors) > 0
        # Check that error messages include DICOM tags
        tag_errors = [error for error in result.errors if '(' in error and ')' in error]
        assert len(tag_errors) > 0
        # Verify specific tags are mentioned
        series_uid_errors = [error for error in result.errors if '(0020,000E)' in error]
        instance_seq_errors = [error for error in result.errors if '(0008,114A)' in error]
        assert len(series_uid_errors) > 0
        assert len(instance_seq_errors) > 0
    
    def test_error_messages_include_type_information(self):
        """Test that error messages include DICOM type information."""
        dataset = Dataset()
        incomplete_item = Dataset()
        dataset.ReferencedSeriesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Check that error messages include Type information
        type_errors = [error for error in result.errors if 'Type 1' in error]
        assert len(type_errors) > 0
    
    def test_error_messages_include_location_context(self):
        """Test that error messages include specific location context for nested sequences."""
        dataset = Dataset()
        series_item = Dataset()
        series_item.SeriesInstanceUID = "*******.*******.9"
        
        incomplete_instance = Dataset()
        # Missing required SOP UIDs
        series_item.ReferencedInstanceSequence = [incomplete_instance]
        dataset.ReferencedSeriesSequence = [series_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Check that error messages include location context
        location_errors = [error for error in result.errors if 'item 0' in error]
        assert len(location_errors) > 0
        # Verify nested location context
        nested_errors = [error for error in result.errors if 'Referenced Series Sequence item 0, Referenced Instance Sequence item 0' in error]
        assert len(nested_errors) > 0
    
    def test_error_messages_provide_guidance(self):
        """Test that error messages provide helpful guidance for resolution."""
        dataset = Dataset()
        incomplete_item = Dataset()
        dataset.ReferencedSeriesSequence = [incomplete_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Check that some error messages provide guidance
        guidance_errors = [error for error in result.errors if 
                          'must include' in error or 'should' in error or 'identifier' in error]
        assert len(guidance_errors) > 0


class TestComplexScenarios:
    """Test complex validation scenarios with multiple sequences and items."""
    
    def test_multiple_series_multiple_instances(self):
        """Test validation with multiple series each containing multiple instances."""
        dataset = Dataset()
        
        # Create multiple valid series
        series1 = self._create_valid_referenced_series_item("*******.*******.9")
        series2 = self._create_valid_referenced_series_item("*******.*******.10")
        
        # Add multiple instances to each series
        series1.ReferencedInstanceSequence.append(self._create_valid_referenced_instance_item("*******.*******.11"))
        series2.ReferencedInstanceSequence.append(self._create_valid_referenced_instance_item("*******.*******.12"))
        
        dataset.ReferencedSeriesSequence = [series1, series2]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
    
    def test_mixed_valid_and_invalid_items(self):
        """Test validation with mix of valid and invalid sequence items."""
        dataset = Dataset()
        
        # Valid series
        valid_series = self._create_valid_referenced_series_item("*******.*******.9")
        
        # Invalid series (missing Series Instance UID)
        invalid_series = Dataset()
        invalid_series.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        
        dataset.ReferencedSeriesSequence = [valid_series, invalid_series]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should have exactly one error for the invalid series
        series_uid_errors = [error for error in result.errors if 'Series Instance UID (0020,000E) is required' in error and 'item 1' in error]
        assert len(series_uid_errors) == 1
    
    def test_both_sequences_with_complex_structure(self):
        """Test validation with both referenced series and other studies sequences."""
        dataset = Dataset()
        
        # Add Referenced Series Sequence
        dataset.ReferencedSeriesSequence = [self._create_valid_referenced_series_item("*******.*******.9")]
        
        # Add Studies Containing Other Referenced Instances Sequence
        study_item = Dataset()
        study_item.StudyInstanceUID = "*******.*******.11"
        study_item.ReferencedSeriesSequence = [self._create_valid_referenced_series_item("*******.*******.12")]
        
        dataset.StudiesContainingOtherReferencedInstancesSequence = [study_item]
        
        result = CommonInstanceReferenceValidator.validate(dataset)
        
        # Should pass validation with no errors
        assert len(result.errors) == 0
    
    def _create_valid_referenced_series_item(self, series_uid: str = "*******.*******.9") -> Dataset:
        """Create a valid referenced series item for testing."""
        item = Dataset()
        item.SeriesInstanceUID = series_uid
        item.ReferencedInstanceSequence = [self._create_valid_referenced_instance_item()]
        return item
    
    def _create_valid_referenced_instance_item(self, instance_uid: str = "*******.*******.10") -> Dataset:
        """Create a valid referenced instance item for testing."""
        item = Dataset()
        item.ReferencedSOPClassUID = "1.2.840.10008.5.1.4.1.1.2"
        item.ReferencedSOPInstanceUID = instance_uid
        return item