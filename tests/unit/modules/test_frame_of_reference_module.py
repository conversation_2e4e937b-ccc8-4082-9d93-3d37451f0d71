"""
Test FrameOfReferenceModule (M - Mandatory) functionality.

FrameOfReferenceModule implements DICOM PS3.3 C.7.4.1 Frame of Reference Module.
Required for all RTDoseIOD instances.
"""

import pytest
from pydicom.uid import generate_uid
from pyrt_dicom.modules import FrameOfReferenceModule
from pyrt_dicom.validators.modules.frame_of_reference_validator import FrameOfReferenceValidator
from pyrt_dicom.validators import ValidationResult


class TestFrameOfReferenceModule:
    """Test FrameOfReferenceModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )
        
        assert frame_ref.FrameOfReferenceUID == frame_uid
    
    def test_frame_uid_uniqueness(self):
        """Test frame of reference UID uniqueness."""
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        
        # UIDs should be different
        assert frame_ref1.FrameOfReferenceUID != frame_ref2.FrameOfReferenceUID
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid(),
            position_reference_indicator="ISOCENTER"
        )
        
        assert hasattr(frame_ref, 'PositionReferenceIndicator')
        assert frame_ref.PositionReferenceIndicator == "ISOCENTER"
    
    def test_specific_frame_uid_format(self):
        """Test specific frame UID format validation."""
        # Test with specific valid UID
        specific_uid = "1.2.826.0.1.3680043.2.1125.1.12345678901234567890"
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=specific_uid
        )
        
        assert frame_ref.FrameOfReferenceUID == specific_uid
    
    def test_position_reference_indicators(self):
        """Test various position reference indicator values."""
        indicators = ["ISOCENTER", "COUCH", "EXTERNAL", "PHANTOM"]
        
        for indicator in indicators:
            frame_ref = FrameOfReferenceModule.from_required_elements(
                frame_of_reference_uid=generate_uid(),
                position_reference_indicator=indicator
            )
            assert frame_ref.PositionReferenceIndicator == indicator
    
    def test_frame_of_reference_for_rt_dose(self):
        """Test frame of reference specific to RT dose requirements."""
        # RT Dose typically uses consistent frame of reference with planning CT
        frame_uid = generate_uid()
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=frame_uid
        )
        
        # Verify UID is preserved for spatial consistency
        assert frame_ref.FrameOfReferenceUID == frame_uid
    
    def test_multiple_references_same_frame(self):
        """Test that multiple objects can reference the same frame."""
        shared_frame_uid = generate_uid()
        
        # Multiple modules using same frame of reference
        frame_ref1 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )
        frame_ref2 = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=shared_frame_uid
        )
        
        # Both should have the same frame UID
        assert frame_ref1.FrameOfReferenceUID == frame_ref2.FrameOfReferenceUID
        assert frame_ref1.FrameOfReferenceUID == shared_frame_uid
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=generate_uid()
        )
        
        assert hasattr(frame_ref, 'validate')
        assert callable(frame_ref.validate)
        
        # Test validation result structure
        validation_result = frame_ref.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_frame_reference_consistency(self):
        """Test frame reference consistency requirements."""
        # Create frame reference for treatment planning consistency
        planning_frame_uid = generate_uid()
        
        frame_ref = FrameOfReferenceModule.from_required_elements(
            frame_of_reference_uid=planning_frame_uid
        )
        
        # Verify consistency for RT dose spatial alignment
        assert frame_ref.FrameOfReferenceUID == planning_frame_uid
