"""Contrast/Bolus Module Validator - DICOM PS3.3 C.7.6.4"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.contrast_ct_enums import ContrastBolusIngredient


class ContrastBolusValidator(BaseValidator):
    """Validator for Contrast/Bolus Module (C.7.6.4)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Contrast/Bolus Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ContrastBolusValidator._validate_enumerated_values(dataset, result)
        
        # Validate flow consistency
        ContrastBolusValidator._validate_flow_consistency(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ContrastBolusValidator._validate_sequence_structures(dataset, result)
        
        # Validate timing consistency
        ContrastBolusValidator._validate_timing_consistency(dataset, result)
        
        # Validate logical consistency (integrate example validation)
        ContrastBolusValidator._validate_logical_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Contrast/Bolus Ingredient (0018,1048)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        if ingredient:
            valid_ingredients = [ingredient_enum.value for ingredient_enum in ContrastBolusIngredient]
            BaseValidator.validate_enumerated_value(
                str(ingredient), valid_ingredients,
                "Contrast/Bolus Ingredient (0018,1048)", result
            )
    
    @staticmethod
    def _validate_flow_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate consistency between flow rate and flow duration."""
        
        flow_rate = getattr(dataset, 'ContrastFlowRate', [])
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # Convert to lists if they are single values (pydicom DSfloat objects)
        if flow_rate and not hasattr(flow_rate, '__iter__'):
            flow_rate = [flow_rate]
        if flow_duration and not hasattr(flow_duration, '__iter__'):
            flow_duration = [flow_duration]
            
        # If both are present, they should have the same number of values
        if flow_rate and flow_duration:
            if len(flow_rate) != len(flow_duration):
                result.add_error(
                    f"Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047) "
                    f"must have the same number of values. "
                    f"Flow Rate has {len(flow_rate)} values, Flow Duration has {len(flow_duration)} values"
                )
    
    @staticmethod
    def _validate_sequence_structures(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structures and their code items."""
        
        # Validate Contrast/Bolus Agent Sequence
        agent_seq = getattr(dataset, 'ContrastBolusAgentSequence', [])
        for i, agent_item in enumerate(agent_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                agent_item, f"Contrast/Bolus Agent Sequence item {i+1}", result
            )
        
        # Validate Administration Route Sequence
        route_seq = getattr(dataset, 'ContrastBolusAdministrationRouteSequence', [])
        if len(route_seq) > 1:
            result.add_error(
                "Contrast/Bolus Administration Route Sequence (0018,0014) "
                f"should contain only a single Item according to DICOM PS3.3 C.7.6.4. "
                f"Found {len(route_seq)} items"
            )
        
        for i, route_item in enumerate(route_seq):
            ContrastBolusValidator._validate_code_sequence_item(
                route_item, f"Administration Route Sequence item {i+1}", result
            )
            
            # Validate nested Additional Drug Sequence
            drug_seq = getattr(route_item, 'AdditionalDrugSequence', [])
            for j, drug_item in enumerate(drug_seq):
                ContrastBolusValidator._validate_code_sequence_item(
                    drug_item, f"Additional Drug Sequence item {j+1} in route item {i+1}", result
                )
    
    @staticmethod
    def _validate_code_sequence_item(item: Dataset, item_description: str, result: ValidationResult) -> None:
        """Validate a code sequence item structure (Code Sequence Macro).
        
        According to DICOM PS3.3 Table 8.8-1 Code Sequence Macro Attributes:
        - Code Value (0008,0100) Type 1
        - Coding Scheme Designator (0008,0102) Type 1  
        - Code Meaning (0008,0104) Type 1
        """
        
        # Code Value is required (Type 1)
        if not hasattr(item, 'CodeValue') or not item.CodeValue:
            result.add_error(
                f"Code Value (0008,0100) is required (Type 1) in {item_description}"
            )
        
        # Coding Scheme Designator is required (Type 1)
        if not hasattr(item, 'CodingSchemeDesignator') or not item.CodingSchemeDesignator:
            result.add_error(
                f"Coding Scheme Designator (0008,0102) is required (Type 1) in {item_description}"
            )
        
        # Code Meaning is required (Type 1) per Code Sequence Macro
        if not hasattr(item, 'CodeMeaning') or not item.CodeMeaning:
            result.add_error(
                f"Code Meaning (0008,0104) is required (Type 1) in {item_description} "
                f"per DICOM PS3.3 Table 8.8-1 Code Sequence Macro"
            )
    
    @staticmethod
    def _validate_timing_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate timing information consistency."""
        
        start_time = getattr(dataset, 'ContrastBolusStartTime', '')
        stop_time = getattr(dataset, 'ContrastBolusStopTime', '')
        flow_duration = getattr(dataset, 'ContrastFlowDuration', [])
        
        # If both start and stop times are present, validate order
        if start_time and stop_time:
            try:
                # Parse DICOM time format (HHMMSS or HHMMSS.FFFFFF)
                start_parts = start_time.split('.')
                stop_parts = stop_time.split('.')
                
                start_hhmmss = start_parts[0]
                stop_hhmmss = stop_parts[0]
                
                if len(start_hhmmss) >= 6 and len(stop_hhmmss) >= 6:
                    start_seconds = (int(start_hhmmss[:2]) * 3600 + 
                                   int(start_hhmmss[2:4]) * 60 + 
                                   int(start_hhmmss[4:6]))
                    stop_seconds = (int(stop_hhmmss[:2]) * 3600 + 
                                  int(stop_hhmmss[2:4]) * 60 + 
                                  int(stop_hhmmss[4:6]))
                    
                    if start_seconds >= stop_seconds:
                        result.add_warning(
                            "Contrast/Bolus Start Time (0018,1042) should be before "
                            "Contrast/Bolus Stop Time (0018,1043)"
                        )
            except (ValueError, IndexError):
                result.add_warning(
                    "Could not validate time order due to invalid time format"
                )
        
        # Validate that flow duration is an alternative to stop time
        if stop_time and flow_duration:
            result.add_warning(
                "Both Contrast/Bolus Stop Time (0018,1043) and Contrast Flow Duration (0018,1047) "
                "are present. Flow duration is an alternate method of specifying stop time"
            )
    
    @staticmethod
    def _validate_logical_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between related attributes.
        
        Uses the module's properties to check consistency based on DICOM standard 
        example and logical relationships between attributes.
        """
        # Import here to avoid circular imports
        from ...modules.modules.contrast_bolus_module import ContrastBolusModule
        
        # If dataset is a ContrastBolusModule, use its properties for consistency checks
        if isinstance(dataset, ContrastBolusModule):
            # Check flow rate and duration consistency
            if not dataset.has_flow_rate_duration_consistency:
                result.add_error(
                    "Contrast Flow Rate (0018,1046) and Contrast Flow Duration (0018,1047) "
                    f"must have the same number of values. Flow Rate has {dataset.get_flow_rate_count()} "
                    f"values, Flow Duration has {dataset.get_flow_duration_count()} values. "
                    f"Each Contrast Flow Duration Value shall correspond to a Value of "
                    f"Contrast Flow Rate per DICOM PS3.3 C.7.6.4"
                )
            
            # Check dose and volume consistency
            if not dataset.has_dose_volume_consistency:
                volume = getattr(dataset, 'ContrastBolusVolume', None)
                total_dose = getattr(dataset, 'ContrastBolusTotalDose', None)
                result.add_warning(
                    f"Contrast/Bolus Total Dose ({total_dose} ml) should not exceed "
                    f"Contrast/Bolus Volume ({volume} ml). Total dose represents "
                    f"undiluted contrast agent amount per DICOM standard example"
                )
            
            # Check ingredient and concentration pairing
            if not dataset.has_ingredient_concentration_pairing:
                ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
                concentration = getattr(dataset, 'ContrastBolusIngredientConcentration', None)
                
                if ingredient and concentration is None:
                    result.add_warning(
                        "Contrast/Bolus Ingredient Concentration (0018,1049) is recommended "
                        "when Contrast/Bolus Ingredient (0018,1048) is specified for "
                        "complete contrast agent characterization"
                    )
                
                if concentration is not None and not ingredient:
                    result.add_warning(
                        "Contrast/Bolus Ingredient (0018,1048) is recommended "
                        "when Contrast/Bolus Ingredient Concentration (0018,1049) is specified "
                        "for complete contrast agent characterization"
                    )
        else:
            # Fallback validation for generic Dataset objects
            ContrastBolusValidator._validate_generic_logical_consistency(dataset, result)
    
    @staticmethod
    def _validate_generic_logical_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency for generic Dataset objects."""
        volume = getattr(dataset, 'ContrastBolusVolume', None)
        total_dose = getattr(dataset, 'ContrastBolusTotalDose', None)
        ingredient = getattr(dataset, 'ContrastBolusIngredient', '')
        concentration = getattr(dataset, 'ContrastBolusIngredientConcentration', None)
        
        # Check dose/volume consistency
        if volume is not None and total_dose is not None:
            if total_dose > volume:
                result.add_warning(
                    f"Contrast/Bolus Total Dose ({total_dose} ml) should not exceed "
                    f"Contrast/Bolus Volume ({volume} ml). Total dose represents "
                    f"undiluted contrast agent amount per DICOM standard example"
                )
        
        # Check ingredient/concentration pairing
        if ingredient and concentration is None:
            result.add_warning(
                "Contrast/Bolus Ingredient Concentration (0018,1049) is recommended "
                "when Contrast/Bolus Ingredient (0018,1048) is specified"
            )
        
        if concentration is not None and not ingredient:
            result.add_warning(
                "Contrast/Bolus Ingredient (0018,1048) is recommended "
                "when Contrast/Bolus Ingredient Concentration (0018,1049) is specified"
            )
