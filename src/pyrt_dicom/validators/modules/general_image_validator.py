"""General Image Module DICOM validation - PS3.3 C.7.6.1"""

from pydicom import Dataset
from pydicom.multival import MultiValue
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.image_enums import (
    QualityControlImage, BurnedInAnnotation, RecognizableVisualFeatures,
    LossyImageCompression, PresentationLUTShape, ImageLaterality,
    LossyImageCompressionMethod
)


class GeneralImageValidator:
    """Validator for DICOM General Image Module (PS3.3 C.7.6.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Image Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            GeneralImageValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            GeneralImageValidator._validate_sequence_requirements(dataset, result)
            GeneralImageValidator._validate_real_world_value_mapping_sequence(dataset, result)
        
        # Validate lossy compression consistency
        if config.validate_conditional_requirements:
            GeneralImageValidator._validate_lossy_compression_consistency(dataset, result)
        
        return result

    @staticmethod
    def _validate_image_type_structure(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Image Type multi-value structure per DICOM PS3.3 C.7.6.1.1.2."""
        image_type = dataset.ImageType

        # Check if it's a multi-value field (list or MultiValue)
        if isinstance(image_type, str):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        # Accept both list and MultiValue types
        if not isinstance(image_type, (list, MultiValue)):
            result.add_error(
                "Image Type (0008,0008) must be multi-valued. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        if len(image_type) < 2:
            result.add_error(
                f"Image Type (0008,0008) must contain at least 2 values, got {len(image_type)}. "
                "Value 1: Pixel Data Characteristics, Value 2: Patient Examination Characteristics. "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
            return

        # Value 1 - Pixel Data Characteristics
        if image_type[0] not in ["ORIGINAL", "DERIVED"]:
            result.add_error(
                f"Image Type (0008,0008) Value 1 (Pixel Data Characteristics) has invalid value '{image_type[0]}'. "
                "Valid values: ORIGINAL (original/source data), DERIVED (derived from other images). "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )

        # Value 2 - Patient Examination Characteristics
        if image_type[1] not in ["PRIMARY", "SECONDARY"]:
            result.add_error(
                f"Image Type (0008,0008) Value 2 (Patient Examination Characteristics) has invalid value '{image_type[1]}'. "
                "Valid values: PRIMARY (direct result of examination), SECONDARY (created after examination). "
                "See DICOM PS3.3 C.7.6.1.1.2."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements per DICOM PS3.3 C.7.6.1."""

        # Type 2C: Patient Orientation conditional requirement
        GeneralImageValidator._validate_patient_orientation_requirement(dataset, result)

        # Type 2C: Content Date and Content Time for temporally related series
        GeneralImageValidator._validate_temporal_requirements(dataset, result)

    @staticmethod
    def _validate_patient_orientation_requirement(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Patient Orientation Type 2C conditional requirement.

        Per DICOM PS3.3 C.7.6.1: Patient Orientation is required if image does not require
        Image Orientation (Patient) (0020,0037) and Image Position (Patient) (0020,0032)
        or if image does not require Image Orientation (Slide) (0048,0102).
        """
        has_patient_orientation = hasattr(dataset, 'PatientOrientation')
        has_image_orientation_patient = hasattr(dataset, 'ImageOrientationPatient')
        has_image_position_patient = hasattr(dataset, 'ImagePositionPatient')
        has_image_orientation_slide = hasattr(dataset, 'ImageOrientationSlide')

        # Check if image has spatial orientation information from other modules
        has_spatial_orientation = (
            (has_image_orientation_patient and has_image_position_patient) or
            has_image_orientation_slide
        )

        # If no spatial orientation from other modules, Patient Orientation should be present
        if not has_spatial_orientation and not has_patient_orientation:
            result.add_warning(
                "Patient Orientation (0020,0020) should be present when image does not have "
                "Image Orientation (Patient) (0020,0037) with Image Position (Patient) (0020,0032) "
                "or Image Orientation (Slide) (0048,0102). See DICOM PS3.3 C.7.6.1.1.1."
            )

        # Validate Patient Orientation format when present
        if has_patient_orientation:
            GeneralImageValidator._validate_patient_orientation_format(dataset, result)

    @staticmethod
    def _validate_patient_orientation_format(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Patient Orientation format requirements."""
        orientation = dataset.PatientOrientation

        # Should be 2 values when not empty
        if orientation != "" and orientation != []:
            if isinstance(orientation, str) and orientation != "":
                # Single string should not be used for non-empty orientation
                result.add_warning(
                    "Patient Orientation (0020,0020) should contain two values designating "
                    "row and column directions when not empty. See DICOM PS3.3 C.7.6.1.1.1."
                )
            elif isinstance(orientation, (list, MultiValue)) and len(orientation) != 2:
                result.add_error(
                    f"Patient Orientation (0020,0020) must contain exactly 2 values, got {len(orientation)}. "
                    "See DICOM PS3.3 C.7.6.1.1.1."
                )

    @staticmethod
    def _validate_temporal_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate temporal element requirements for temporally related series."""
        has_content_date = hasattr(dataset, 'ContentDate')
        has_content_time = hasattr(dataset, 'ContentTime')

        if has_content_date and not has_content_time:
            result.add_warning(
                "Content Time (0008,0033) should be present when Content Date (0008,0023) is present "
                "for temporally related series. See DICOM PS3.3 C.7.6.1."
            )
        elif has_content_time and not has_content_date:
            result.add_warning(
                "Content Date (0008,0023) should be present when Content Time (0008,0033) is present "
                "for temporally related series. See DICOM PS3.3 C.7.6.1."
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM standard."""
        
        # Quality Control Image
        if hasattr(dataset, 'QualityControlImage'):
            valid_values = [e.value for e in QualityControlImage]
            if dataset.QualityControlImage not in valid_values:
                result.add_error(
                    f"Quality Control Image (0028,0300) has invalid value '{dataset.QualityControlImage}'. "
                    f"Valid values: {valid_values}. Indicates presence of quality control material. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Burned In Annotation
        if hasattr(dataset, 'BurnedInAnnotation'):
            valid_values = [e.value for e in BurnedInAnnotation]
            if dataset.BurnedInAnnotation not in valid_values:
                result.add_error(
                    f"Burned In Annotation (0028,0301) has invalid value '{dataset.BurnedInAnnotation}'. "
                    f"Valid values: {valid_values}. Indicates sufficient annotation to identify patient. "
                    "See DICOM PS3.3 C.7.6.1."
                )
        
        # Recognizable Visual Features
        if hasattr(dataset, 'RecognizableVisualFeatures'):
            valid_values = [e.value for e in RecognizableVisualFeatures]
            if dataset.RecognizableVisualFeatures not in valid_values:
                result.add_error(
                    f"Recognizable Visual Features (0028,0302) has invalid value '{dataset.RecognizableVisualFeatures}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Lossy Image Compression
        if hasattr(dataset, 'LossyImageCompression'):
            valid_values = [e.value for e in LossyImageCompression]
            if dataset.LossyImageCompression not in valid_values:
                result.add_error(
                    f"Lossy Image Compression (0028,2110) has invalid value '{dataset.LossyImageCompression}'. "
                    f"Valid values: {valid_values}. Once set to '01', shall not be reset. "
                    "See DICOM PS3.3 C.7.6.1.1.5."
                )
        
        # Presentation LUT Shape
        if hasattr(dataset, 'PresentationLUTShape'):
            valid_values = [e.value for e in PresentationLUTShape]
            if dataset.PresentationLUTShape not in valid_values:
                result.add_error(
                    f"Presentation LUT Shape (2050,0020) has invalid value '{dataset.PresentationLUTShape}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Image Laterality
        if hasattr(dataset, 'ImageLaterality'):
            valid_values = [e.value for e in ImageLaterality]
            if dataset.ImageLaterality not in valid_values:
                result.add_error(
                    f"Image Laterality (0020,0062) has invalid value '{dataset.ImageLaterality}'. "
                    f"Valid values: {valid_values}"
                )
        
        # Lossy Image Compression Method
        if hasattr(dataset, 'LossyImageCompressionMethod'):
            valid_values = [e.value for e in LossyImageCompressionMethod]
            methods = dataset.LossyImageCompressionMethod
            if isinstance(methods, str):
                methods = [methods]
            for method in methods:
                if method not in valid_values:
                    result.add_error(
                        f"Lossy Image Compression Method (0028,2114) has invalid value '{method}'. "
                        f"Valid values: {valid_values}"
                    )
        
        # Image Type validation
        if hasattr(dataset, 'ImageType'):
            GeneralImageValidator._validate_image_type_structure(dataset, result)
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Icon Image Sequence validation
        if hasattr(dataset, 'IconImageSequence'):
            if len(dataset.IconImageSequence) > 1:
                result.add_error(
                    "Icon Image Sequence (0088,0200) may contain only a single Item"
                )
            
            if len(dataset.IconImageSequence) == 1:
                icon_item = dataset.IconImageSequence[0]
                
                # Required attributes for icon image
                required_attrs = ['Rows', 'Columns', 'SamplesPerPixel', 'PhotometricInterpretation',
                                'BitsAllocated', 'BitsStored', 'HighBit', 'PixelRepresentation']
                
                for attr in required_attrs:
                    if not hasattr(icon_item, attr):
                        result.add_error(
                            f"Icon Image Sequence item missing required attribute {attr}"
                        )
                
                # Validate icon image constraints
                if hasattr(icon_item, 'SamplesPerPixel') and icon_item.SamplesPerPixel != 1:
                    if hasattr(icon_item, 'PhotometricInterpretation'):
                        if icon_item.PhotometricInterpretation not in ["MONOCHROME1", "MONOCHROME2", "PALETTE COLOR"]:
                            result.add_error(
                                "Icon Image must use monochrome or palette color photometric interpretation"
                            )
                
                if hasattr(icon_item, 'BitsAllocated'):
                    if icon_item.BitsAllocated not in [1, 8]:
                        result.add_error(
                            "Icon Image Bits Allocated must be 1 or 8"
                        )
    
    @staticmethod
    def _validate_lossy_compression_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate lossy compression attribute consistency."""
        
        has_lossy_compression = hasattr(dataset, 'LossyImageCompression')
        has_compression_ratio = hasattr(dataset, 'LossyImageCompressionRatio')
        has_compression_method = hasattr(dataset, 'LossyImageCompressionMethod')
        
        # If lossy compression is "01", ratio and method should be present
        if has_lossy_compression and dataset.LossyImageCompression == "01":
            if not has_compression_ratio:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'"
                )
            if not has_compression_method:
                result.add_warning(
                    "Lossy Image Compression Method (0028,2114) should be present when "
                    "Lossy Image Compression (0028,2110) is '01'"
                )
        
        # If ratio and method are both present, they should have corresponding values
        if has_compression_ratio and has_compression_method:
            ratio_count = len(dataset.LossyImageCompressionRatio) if isinstance(dataset.LossyImageCompressionRatio, list) else 1
            method_count = len(dataset.LossyImageCompressionMethod) if isinstance(dataset.LossyImageCompressionMethod, list) else 1
            
            if ratio_count != method_count:
                result.add_warning(
                    "Lossy Image Compression Ratio (0028,2112) and Method (0028,2114) "
                    "should have corresponding number of values"
                )

    @staticmethod
    def _validate_real_world_value_mapping_sequence(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Real World Value Mapping Sequence structure."""
        if not hasattr(dataset, 'RealWorldValueMappingSequence'):
            return

        sequence = dataset.RealWorldValueMappingSequence
        # pydicom sequences are already lists, so this check is not needed
        # if not isinstance(sequence, list):
        #     result.add_error(
        #         "Real World Value Mapping Sequence (0040,9096) must be a sequence (list)"
        #     )
        #     return

        for i, item in enumerate(sequence):
            if not hasattr(item, 'RealWorldValueMappingSequence') and \
               not hasattr(item, 'RealWorldValueFirstValueMapped') and \
               not hasattr(item, 'RealWorldValueLastValueMapped') and \
               not hasattr(item, 'RealWorldValueLUTData'):
                result.add_warning(
                    f"Real World Value Mapping Sequence item {i+1} appears to be empty. "
                    "Consider including mapping attributes for proper real world value conversion."
                )
