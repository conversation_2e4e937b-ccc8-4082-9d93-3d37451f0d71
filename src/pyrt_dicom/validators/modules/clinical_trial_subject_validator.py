"""Clinical Trial Subject Module DICOM validation - PS3.3 C.7.1.3"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ClinicalTrialSubjectValidator:
    """Validator for DICOM Clinical Trial Subject Module (PS3.3 C.7.1.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Clinical Trial Subject Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 required elements
        ClinicalTrialSubjectValidator._validate_type_1_elements(dataset, result)
        
        # Validate Type 2 present elements  
        ClinicalTrialSubjectValidator._validate_type_2_elements(dataset, result)
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            ClinicalTrialSubjectValidator._validate_conditional_requirements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ClinicalTrialSubjectValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_type_1_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 (required) elements per DICOM PS3.3 C.7.1.3."""
        
        # Clinical Trial Sponsor Name (0012,0010) Type 1
        if not hasattr(dataset, 'ClinicalTrialSponsorName') or not getattr(dataset, 'ClinicalTrialSponsorName', '').strip():
            result.add_error(
                "Clinical Trial Sponsor Name (0012,0010) is required (Type 1) per DICOM PS3.3 C.7.1.3"
            )
        
        # Clinical Trial Protocol ID (0012,0020) Type 1  
        if not hasattr(dataset, 'ClinicalTrialProtocolID') or not getattr(dataset, 'ClinicalTrialProtocolID', '').strip():
            result.add_error(
                "Clinical Trial Protocol ID (0012,0020) is required (Type 1) per DICOM PS3.3 C.7.1.3"
            )
    
    @staticmethod
    def _validate_type_2_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2 (required but can be empty) elements per DICOM PS3.3 C.7.1.3."""
        
        # Clinical Trial Protocol Name (0012,0021) Type 2
        if not hasattr(dataset, 'ClinicalTrialProtocolName'):
            result.add_error(
                "Clinical Trial Protocol Name (0012,0021) must be present (Type 2) per DICOM PS3.3 C.7.1.3"
            )
        
        # Clinical Trial Site ID (0012,0030) Type 2
        if not hasattr(dataset, 'ClinicalTrialSiteID'):
            result.add_error(
                "Clinical Trial Site ID (0012,0030) must be present (Type 2) per DICOM PS3.3 C.7.1.3"
            )
        
        # Clinical Trial Site Name (0012,0031) Type 2
        if not hasattr(dataset, 'ClinicalTrialSiteName'):
            result.add_error(
                "Clinical Trial Site Name (0012,0031) must be present (Type 2) per DICOM PS3.3 C.7.1.3"
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements per DICOM PS3.3 C.7.1.3."""
        
        # Type 1C: Clinical Trial Subject ID (0012,0040) or Clinical Trial Subject Reading ID (0012,0042) required
        # "Shall be present if Clinical Trial Subject Reading ID (0012,0042) is absent. May be present otherwise."
        # "Shall be present if Clinical Trial Subject ID (0012,0040) is absent. May be present otherwise."
        has_subject_id = hasattr(dataset, 'ClinicalTrialSubjectID') and getattr(dataset, 'ClinicalTrialSubjectID', '').strip()
        has_reading_id = hasattr(dataset, 'ClinicalTrialSubjectReadingID') and getattr(dataset, 'ClinicalTrialSubjectReadingID', '').strip()
        
        if not has_subject_id and not has_reading_id:
            result.add_error(
                "Either Clinical Trial Subject ID (0012,0040) or "
                "Clinical Trial Subject Reading ID (0012,0042) is required (Type 1C) "
                "per DICOM PS3.3 C.7.1.3"
            )
        
        # Type 1C: Ethics Committee Name (0012,0081) required if Ethics Committee Approval Number (0012,0082) is present
        # "Required if Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present."
        has_approval_number = (hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber') and 
                              getattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber', '').strip())
        has_committee_name = (hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName') and 
                             getattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName', '').strip())
        
        if has_approval_number and not has_committee_name:
            result.add_error(
                "Clinical Trial Protocol Ethics Committee Name (0012,0081) is required (Type 1C) when "
                "Clinical Trial Protocol Ethics Committee Approval Number (0012,0082) is present "
                "per DICOM PS3.3 C.7.1.3"
            )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements per DICOM PS3.3 C.7.1.3."""
        
        # Other Clinical Trial Protocol IDs Sequence (0012,0023) validation
        if hasattr(dataset, 'OtherClinicalTrialProtocolIDsSequence'):
            other_protocol_ids_seq = getattr(dataset, 'OtherClinicalTrialProtocolIDsSequence', [])
            for i, item in enumerate(other_protocol_ids_seq):
                # Check that sequence item is a Dataset
                if not isinstance(item, Dataset):
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Sequence items must be pydicom Dataset objects"
                    )
                    continue
                
                # Clinical Trial Protocol ID (0012,0020) Type 1 within sequence
                if not hasattr(item, 'ClinicalTrialProtocolID') or not getattr(item, 'ClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Clinical Trial Protocol ID (0012,0020) is required (Type 1) "
                        "per DICOM PS3.3 C.7.1.3"
                    )
                
                # Issuer of Clinical Trial Protocol ID (0012,0022) Type 1 within sequence
                if not hasattr(item, 'IssuerOfClinicalTrialProtocolID') or not getattr(item, 'IssuerOfClinicalTrialProtocolID', '').strip():
                    result.add_error(
                        f"Other Clinical Trial Protocol IDs Sequence item {i}: "
                        "Issuer of Clinical Trial Protocol ID (0012,0022) is required (Type 1) "
                        "per DICOM PS3.3 C.7.1.3"
                    )
