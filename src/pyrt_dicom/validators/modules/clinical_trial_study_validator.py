"""Clinical Trial Study Module DICOM validation - PS3.3 C.7.2.3

Validates Clinical Trial Study Module according to DICOM standard requirements
including Type 1C conditional logic for temporal events and consent sequences.
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ClinicalTrialStudyValidator:
    """Validator for DICOM Clinical Trial Study Module (PS3.3 C.7.2.3)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Clinical Trial Study Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            ClinicalTrialStudyValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ClinicalTrialStudyValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            ClinicalTrialStudyValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Longitudinal Temporal Event Type required if Longitudinal Temporal Offset from Event is present
        has_offset = hasattr(dataset, 'LongitudinalTemporalOffsetFromEvent')
        if has_offset and not hasattr(dataset, 'LongitudinalTemporalEventType'):
            result.add_error(
                "Longitudinal Temporal Event Type (0012,0053) is required when "
                "Longitudinal Temporal Offset from Event (0012,0052) is present"
            )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Longitudinal Temporal Event Type (0012,0053)
        event_type = getattr(dataset, 'LongitudinalTemporalEventType', '')
        if event_type:
            BaseValidator.validate_enumerated_value(
                event_type, ["ENROLLMENT", "BASELINE"],
                "Longitudinal Temporal Event Type (0012,0053)", result
            )
        
        # Validate enumerated values within Consent for Clinical Trial Use Sequence
        consent_seq = getattr(dataset, 'ConsentForClinicalTrialUseSequence', [])
        for i, item in enumerate(consent_seq):
            # Consent for Distribution Flag (0012,0085)
            flag_value = getattr(item, 'ConsentForDistributionFlag', '')
            if flag_value:
                BaseValidator.validate_enumerated_value(
                    flag_value, ["NO", "YES", "WITHDRAWN"],
                    f"Consent for Clinical Trial Use Sequence item {i}: Consent for Distribution Flag (0012,0085)", result
                )
            
            # Distribution Type (0012,0084)
            dist_type = getattr(item, 'DistributionType', '')
            if dist_type:
                BaseValidator.validate_enumerated_value(
                    dist_type, ["NAMED_PROTOCOL", "RESTRICTED_REUSE", "PUBLIC_RELEASE"],
                    f"Consent for Clinical Trial Use Sequence item {i}: Distribution Type (0012,0084)", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Consent for Clinical Trial Use Sequence validation
        consent_seq = getattr(dataset, 'ConsentForClinicalTrialUseSequence', [])
        for i, item in enumerate(consent_seq):
            # Type 1: Consent for Distribution Flag is required within sequence
            if not hasattr(item, 'ConsentForDistributionFlag') or not getattr(item, 'ConsentForDistributionFlag', ''):
                result.add_error(
                    f"Consent for Clinical Trial Use Sequence item {i}: "
                    "Consent for Distribution Flag (0012,0085) is required (Type 1)"
                )
            else:
                flag_value = getattr(item, 'ConsentForDistributionFlag', '')
                
                # Type 1C: Distribution Type required if flag is YES or WITHDRAWN
                if flag_value in ["YES", "WITHDRAWN"]:
                    if not hasattr(item, 'DistributionType') or not getattr(item, 'DistributionType', ''):
                        result.add_error(
                            f"Consent for Clinical Trial Use Sequence item {i}: "
                            "Distribution Type (0012,0084) is required when "
                            "Consent for Distribution Flag (0012,0085) is YES or WITHDRAWN (Type 1C)"
                        )
                    else:
                        dist_type = getattr(item, 'DistributionType', '')
                        
                        # Type 1C: Clinical Trial Protocol ID required if Distribution Type is NAMED_PROTOCOL
                        if dist_type == "NAMED_PROTOCOL":
                            if not hasattr(item, 'ClinicalTrialProtocolID') or not getattr(item, 'ClinicalTrialProtocolID', ''):
                                result.add_error(
                                    f"Consent for Clinical Trial Use Sequence item {i}: "
                                    "Clinical Trial Protocol ID (0012,0020) is required when "
                                    "Distribution Type (0012,0084) is NAMED_PROTOCOL (Type 1C)"
                                )
        
        # Validate Clinical Trial Time Point Type Code Sequence structure
        time_point_seq = getattr(dataset, 'ClinicalTrialTimePointTypeCodeSequence', [])
        for i, item in enumerate(time_point_seq):
            # Validate Code Sequence Macro attributes if present
            required_code_attrs = ['CodeValue', 'CodingSchemeDesignator', 'CodeMeaning']
            for attr in required_code_attrs:
                if not hasattr(item, attr) or not getattr(item, attr, ''):
                    result.add_error(
                        f"Clinical Trial Time Point Type Code Sequence item {i}: "
                        f"{attr} is required for Code Sequence Macro"
                    )
