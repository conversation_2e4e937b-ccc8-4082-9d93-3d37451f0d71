"""Common Instance Reference Module DICOM validation - PS3.3 C.12.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class CommonInstanceReferenceValidator:
    """Validator for DICOM Common Instance Reference Module (PS3.3 C.12.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Common Instance Reference Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            CommonInstanceReferenceValidator._validate_conditional_requirements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            CommonInstanceReferenceValidator._validate_sequence_requirements(dataset, result)
        
        # Validate SOP Instance Reference Macro requirements
        if config.validate_sequences:
            CommonInstanceReferenceValidator._validate_sop_instance_reference_macro(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements.
        
        From DICOM PS3.3 C.12.2:
        - Referenced Series Sequence (0008,1115) is Type 1C: Required if this Instance references Instances in this Study
        - Studies Containing Other Referenced Instances Sequence (0008,1200) is Type 1C: Required if this Instance references Instances in other Studies
        
        Note: Both sequences can be present if instances are referenced both within the same study and in other studies.
        Neither sequence is required if no instances are referenced.
        """
        has_referenced_series = hasattr(dataset, 'ReferencedSeriesSequence') and getattr(dataset, 'ReferencedSeriesSequence', [])
        has_other_studies = hasattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence') and getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        
        # If neither sequence is present, this is valid (no references)
        if not has_referenced_series and not has_other_studies:
            return
        
        # Both sequences present is also valid (references to both same study and other studies)
        # Only one sequence present is also valid (references to either same study or other studies)
        # The conditional validation here focuses on sequence structure rather than study context,
        # as study context validation requires external information about the containing study
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure and Type 1 element requirements."""
        
        # Validate Referenced Series Sequence structure
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for i, series_item in enumerate(ref_series_seq):
            # Series Instance UID is Type 1 in Referenced Series Sequence
            if not series_item.get('SeriesInstanceUID'):
                result.add_error(
                    f"Referenced Series Sequence item {i}: Series Instance UID (0020,000E) is required (Type 1). "
                    f"Each series reference must include a unique series identifier."
                )
            
            # Referenced Instance Sequence is Type 1 within Referenced Series Sequence
            if 'ReferencedInstanceSequence' not in series_item:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is required (Type 1). "
                    f"At least one instance must be referenced within each series."
                )
            elif len(series_item.get('ReferencedInstanceSequence', [])) == 0:
                result.add_warning(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is empty. "
                    f"At least one instance should be referenced within each series."
                )
        
        # Validate Studies Containing Other Referenced Instances Sequence structure  
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for i, study_item in enumerate(other_studies_seq):
            # Study Instance UID is Type 1 in Studies Containing Other Referenced Instances Sequence
            if not study_item.get('StudyInstanceUID'):
                result.add_error(
                    f"Studies Containing Other Referenced Instances Sequence item {i}: "
                    f"Study Instance UID (0020,000D) is required (Type 1). "
                    f"Each study reference must include a unique study identifier."
                )
            
            # Validate Series and Instance Reference Macro (Table 10-4) if present
            if 'ReferencedSeriesSequence' in study_item:
                ref_series_seq_in_study = study_item.get('ReferencedSeriesSequence', [])
                for j, series_item in enumerate(ref_series_seq_in_study):
                    if not series_item.get('SeriesInstanceUID'):
                        result.add_error(
                            f"Studies Containing Other Referenced Instances Sequence item {i}, "
                            f"Referenced Series Sequence item {j}: Series Instance UID (0020,000E) is required (Type 1)"
                        )
    
    @staticmethod
    def _validate_sop_instance_reference_macro(dataset: Dataset, result: ValidationResult) -> None:
        """Validate SOP Instance Reference Macro (Table 10-11) requirements within sequences."""
        
        def validate_referenced_instance_item(item: Dataset, location: str) -> None:
            """Validate a single referenced instance item."""
            # Type 1 requirements
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(f"{location}: Referenced SOP Class UID (0008,1150) is required (Type 1)")
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(f"{location}: Referenced SOP Instance UID (0008,1155) is required (Type 1)")
            
            # Type 1C conditional validation for Referenced Frame Number
            # Would require knowledge of whether the referenced SOP Instance is multi-frame
            # This is a limitation that requires external context for full validation
            
            # Type 1C conditional validation for Referenced Segment Number  
            # Would require knowledge of whether the referenced SOP Instance is a Segmentation
            # This is a limitation that requires external context for full validation
        
        # Validate Referenced Series Sequence items
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for i, series_item in enumerate(ref_series_seq):
            ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
            for j, instance_item in enumerate(ref_instance_seq):
                location = f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}"
                validate_referenced_instance_item(instance_item, location)
        
        # Validate Studies Containing Other Referenced Instances Sequence items
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for i, study_item in enumerate(other_studies_seq):
            if 'ReferencedSeriesSequence' in study_item:
                ref_series_seq = study_item.get('ReferencedSeriesSequence', [])
                for j, series_item in enumerate(ref_series_seq):
                    if 'ReferencedInstanceSequence' in series_item:
                        ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
                        for k, instance_item in enumerate(ref_instance_seq):
                            location = (f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                       f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}")
                            validate_referenced_instance_item(instance_item, location)
