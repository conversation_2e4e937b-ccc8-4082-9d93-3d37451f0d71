"""General Reference Module DICOM validation - PS3.3 C.12.4"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.common_enums import SpatialLocationsPreserved


class GeneralReferenceValidator:
    """Validator for DICOM General Reference Module (PS3.3 C.12.4)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate General Reference Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1C conditional requirements
        if config.validate_conditional_requirements:
            GeneralReferenceValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            GeneralReferenceValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            GeneralReferenceValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements."""
        
        # Type 1C: Patient Orientation required if Spatial Locations Preserved is REORIENTED_ONLY
        source_image_seq = getattr(dataset, 'SourceImageSequence', [])
        for i, item in enumerate(source_image_seq):
            spatial_preserved = getattr(item, 'SpatialLocationsPreserved', '')
            if spatial_preserved == "REORIENTED_ONLY":
                if not getattr(item, 'PatientOrientation', None):
                    result.add_error(
                        f"Source Image Sequence item {i}: Patient Orientation (0020,0020) is required "
                        f"when Spatial Locations Preserved (0028,135A) is REORIENTED_ONLY"
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # Spatial Locations Preserved (0028,135A) in Source Image Sequence
        source_image_seq = getattr(dataset, 'SourceImageSequence', [])
        for i, item in enumerate(source_image_seq):
            spatial_preserved = getattr(item, 'SpatialLocationsPreserved', '')
            if spatial_preserved:
                valid_values = [val.value for val in SpatialLocationsPreserved]
                BaseValidator.validate_enumerated_value(
                    spatial_preserved, valid_values,
                    f"Source Image Sequence item {i}: Spatial Locations Preserved (0028,135A)", result
                )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Referenced Image Sequence - each item needs SOP Class and Instance UID
        ref_image_seq = getattr(dataset, 'ReferencedImageSequence', [])
        for i, item in enumerate(ref_image_seq):
            if not getattr(item, 'ReferencedSOPClassUID', None):
                result.add_error(
                    f"Referenced Image Sequence item {i}: Referenced SOP Class UID (0008,1150) is required"
                )
            if not getattr(item, 'ReferencedSOPInstanceUID', None):
                result.add_error(
                    f"Referenced Image Sequence item {i}: Referenced SOP Instance UID (0008,1155) is required"
                )
        
        # Referenced Instance Sequence - each item needs SOP Class UID, Instance UID, and Purpose of Reference
        ref_instance_seq = getattr(dataset, 'ReferencedInstanceSequence', [])
        for i, item in enumerate(ref_instance_seq):
            if not getattr(item, 'ReferencedSOPClassUID', None):
                result.add_error(
                    f"Referenced Instance Sequence item {i}: Referenced SOP Class UID (0008,1150) is required"
                )
            if not getattr(item, 'ReferencedSOPInstanceUID', None):
                result.add_error(
                    f"Referenced Instance Sequence item {i}: Referenced SOP Instance UID (0008,1155) is required"
                )
            if not getattr(item, 'PurposeOfReferenceCodeSequence', None):
                result.add_error(
                    f"Referenced Instance Sequence item {i}: Purpose of Reference Code Sequence (0040,A170) is required"
                )
        
        # Source Image Sequence - each item needs SOP Class and Instance UID
        source_image_seq = getattr(dataset, 'SourceImageSequence', [])
        for i, item in enumerate(source_image_seq):
            if not getattr(item, 'ReferencedSOPClassUID', None):
                result.add_error(
                    f"Source Image Sequence item {i}: Referenced SOP Class UID (0008,1150) is required"
                )
            if not getattr(item, 'ReferencedSOPInstanceUID', None):
                result.add_error(
                    f"Source Image Sequence item {i}: Referenced SOP Instance UID (0008,1155) is required"
                )

        # Source Instance Sequence - each item needs SOP Class and Instance UID
        source_instance_seq = getattr(dataset, 'SourceInstanceSequence', [])
        for i, item in enumerate(source_instance_seq):
            if not getattr(item, 'ReferencedSOPClassUID', None):
                result.add_error(
                    f"Source Instance Sequence item {i}: Referenced SOP Class UID (0008,1150) is required"
                )
            if not getattr(item, 'ReferencedSOPInstanceUID', None):
                result.add_error(
                    f"Source Instance Sequence item {i}: Referenced SOP Instance UID (0008,1155) is required"
                )
