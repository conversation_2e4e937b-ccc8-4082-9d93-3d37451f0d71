"""
Clinical Trial Subject Module - DICOM PS3.3 C.7.1.3

The Clinical Trial Subject Module contains attributes that identify a Patient 
as a clinical trial or research Subject.
"""
from pydicom import Dataset
from .base_module import BaseModule
from ...validators.modules.base_validator import ValidationConfig
from ...validators.modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
from ...validators import ValidationResult


class ClinicalTrialSubjectModule(BaseModule):
    """Clinical Trial Subject Module implementation for DICOM PS3.3 C.7.1.3.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify a Patient as a clinical trial or research Subject.
    
    Usage:
        # Create with required elements
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001",
            clinical_trial_protocol_name="Phase II Cancer Study",
            clinical_trial_site_id="SITE-001",
            clinical_trial_site_name="University Medical Center"
        )
        
        # Add subject identification (Type 1C - at least one required)
        trial_subject.with_subject_identification(
            clinical_trial_subject_id="SUBJ-12345"
        )
        
        # Add ethics committee info (Type 1C - name required if approval number provided)
        trial_subject.with_ethics_committee(
            clinical_trial_protocol_ethics_committee_name="IRB Committee",
            clinical_trial_protocol_ethics_committee_approval_number="IRB-2024-001"
        )
        
        # Add optional elements
        trial_subject.with_optional_elements(
            issuer_of_clinical_trial_protocol_id="FDA",
            issuer_of_clinical_trial_site_id="SPONSOR"
        )
        
        # Validate
        result = trial_subject.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        clinical_trial_sponsor_name: str,
        clinical_trial_protocol_id: str,
        clinical_trial_protocol_name: str = "",
        clinical_trial_site_id: str = "",
        clinical_trial_site_name: str = ""
    ) -> 'ClinicalTrialSubjectModule':
        """Create ClinicalTrialSubjectModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            clinical_trial_sponsor_name (str): Name of the clinical trial or research sponsor (0012,0010) Type 1
            clinical_trial_protocol_id (str): Identifier for the protocol (0012,0020) Type 1
            clinical_trial_protocol_name (str): Name of the clinical trial or research protocol (0012,0021) Type 2
            clinical_trial_site_id (str): Identifier of the site responsible for submitting data (0012,0030) Type 2
            clinical_trial_site_name (str): Name of the site responsible for submitting data (0012,0031) Type 2
            
        Returns:
            ClinicalTrialSubjectModule: New dataset instance with required data elements set
        """
        instance = cls()
        instance.ClinicalTrialSponsorName = clinical_trial_sponsor_name
        instance.ClinicalTrialProtocolID = clinical_trial_protocol_id
        instance.ClinicalTrialProtocolName = clinical_trial_protocol_name
        instance.ClinicalTrialSiteID = clinical_trial_site_id
        instance.ClinicalTrialSiteName = clinical_trial_site_name
        return instance
    
    def with_optional_elements(
        self,
        issuer_of_clinical_trial_protocol_id: str | None = None,
        other_clinical_trial_protocol_ids_sequence: list[Dataset] | None = None,
        issuer_of_clinical_trial_site_id: str | None = None,
        issuer_of_clinical_trial_subject_id: str | None = None,
        issuer_of_clinical_trial_subject_reading_id: str | None = None,
        clinical_trial_protocol_ethics_committee_approval_number: str | None = None
    ) -> 'ClinicalTrialSubjectModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            issuer_of_clinical_trial_protocol_id (str | None): Identifier of the Assigning Authority (0012,0022) Type 3
            other_clinical_trial_protocol_ids_sequence (list[Dataset] | None): Additional protocol identifiers (0012,0023) Type 3
            issuer_of_clinical_trial_site_id (str | None): Identifier of the Assigning Authority for site ID (0012,0032) Type 3
            issuer_of_clinical_trial_subject_id (str | None): Identifier of the Assigning Authority for subject ID (0012,0041) Type 3
            issuer_of_clinical_trial_subject_reading_id (str | None): Identifier of the Assigning Authority for reading ID (0012,0043) Type 3
            clinical_trial_protocol_ethics_committee_approval_number (str | None): Approval number from ethics committee (0012,0082) Type 3
            
        Returns:
            ClinicalTrialSubjectModule: Self with optional elements added
        """
        self._set_attribute_if_not_none('IssuerOfClinicalTrialProtocolID', issuer_of_clinical_trial_protocol_id)
        self._set_attribute_if_not_none('OtherClinicalTrialProtocolIDsSequence', other_clinical_trial_protocol_ids_sequence)
        self._set_attribute_if_not_none('IssuerOfClinicalTrialSiteID', issuer_of_clinical_trial_site_id)
        self._set_attribute_if_not_none('IssuerOfClinicalTrialSubjectID', issuer_of_clinical_trial_subject_id)
        self._set_attribute_if_not_none('IssuerOfClinicalTrialSubjectReadingID', issuer_of_clinical_trial_subject_reading_id)
        self._set_attribute_if_not_none('ClinicalTrialProtocolEthicsCommitteeApprovalNumber', clinical_trial_protocol_ethics_committee_approval_number)
        return self
    
    def with_subject_identification(
        self,
        clinical_trial_subject_id: str | None = None,
        clinical_trial_subject_reading_id: str | None = None
    ) -> 'ClinicalTrialSubjectModule':
        """Add subject identification with required conditional logic.
        
        Note: Either Clinical Trial Subject ID OR Clinical Trial Subject Reading ID is Type 1C required.
        At least one must be present.
        
        Args:
            clinical_trial_subject_id (str | None): Assigned identifier for the clinical trial subject (0012,0040) Type 1C
            clinical_trial_subject_reading_id (str | None): Identifier for blinded evaluations (0012,0042) Type 1C
            
        Returns:
            ClinicalTrialSubjectModule: Self with subject identification elements added
            
        Raises:
            ValueError: If neither subject ID nor reading ID is provided
        """
        # Type 1C requirement: Either subject ID OR reading ID must be present
        if not clinical_trial_subject_id and not clinical_trial_subject_reading_id:
            raise ValueError(
                "Either clinical_trial_subject_id or clinical_trial_subject_reading_id is required "
                "for Clinical Trial Subject Module (DICOM PS3.3 C.7.1.3)"
            )
        
        self._set_attribute_if_not_none('ClinicalTrialSubjectID', clinical_trial_subject_id)
        self._set_attribute_if_not_none('ClinicalTrialSubjectReadingID', clinical_trial_subject_reading_id)
        return self
    
    def with_ethics_committee(
        self,
        clinical_trial_protocol_ethics_committee_name: str | None = None,
        clinical_trial_protocol_ethics_committee_approval_number: str | None = None
    ) -> 'ClinicalTrialSubjectModule':
        """Add ethics committee information with required conditional logic.
        
        Note: Ethics Committee Name (0012,0081) is Type 1C - required if 
        Ethics Committee Approval Number is present.
        
        Args:
            clinical_trial_protocol_ethics_committee_name (str | None): Name of the Ethics Committee or IRB (0012,0081) Type 1C
            clinical_trial_protocol_ethics_committee_approval_number (str | None): Approval number from committee (0012,0082) Type 3
            
        Returns:
            ClinicalTrialSubjectModule: Self with ethics committee elements added
            
        Raises:
            ValueError: If approval number is provided without committee name
        """
        # Type 1C requirement: Ethics Committee Name required if Approval Number is present
        if clinical_trial_protocol_ethics_committee_approval_number and not clinical_trial_protocol_ethics_committee_name:
            raise ValueError(
                "clinical_trial_protocol_ethics_committee_name is required when "
                "clinical_trial_protocol_ethics_committee_approval_number is provided "
                "(DICOM PS3.3 C.7.1.3)"
            )
        
        self._set_attribute_if_not_none('ClinicalTrialProtocolEthicsCommitteeName', clinical_trial_protocol_ethics_committee_name)
        self._set_attribute_if_not_none('ClinicalTrialProtocolEthicsCommitteeApprovalNumber', clinical_trial_protocol_ethics_committee_approval_number)
        return self
    
    @staticmethod
    def create_other_protocol_id_item(
        clinical_trial_protocol_id: str,
        issuer_of_clinical_trial_protocol_id: str
    ) -> Dataset:
        """Create an item for Other Clinical Trial Protocol IDs Sequence (0012,0023).

        Args:
            clinical_trial_protocol_id (str): Identifier for the protocol (0012,0020) Type 1
            issuer_of_clinical_trial_protocol_id (str): Identifier of the Assigning Authority (0012,0022) Type 1

        Returns:
            Dataset: Sequence item with protocol ID and issuer
        """
        item = Dataset()
        item.ClinicalTrialProtocolID = clinical_trial_protocol_id
        item.IssuerOfClinicalTrialProtocolID = issuer_of_clinical_trial_protocol_id
        return item
    
    @property
    def has_subject_identification(self) -> bool:
        """Check if subject identification is present.
        
        Returns:
            bool: True if either subject ID or reading ID is present
        """
        return (hasattr(self, 'ClinicalTrialSubjectID') or 
                hasattr(self, 'ClinicalTrialSubjectReadingID'))
    
    @property
    def has_ethics_committee_info(self) -> bool:
        """Check if ethics committee information is present.
        
        Returns:
            bool: True if ethics committee name is present
        """
        return hasattr(self, 'ClinicalTrialProtocolEthicsCommitteeName')
    
    @property
    def has_additional_protocol_ids(self) -> bool:
        """Check if additional protocol IDs are present.
        
        Returns:
            bool: True if other protocol IDs sequence is present
        """
        return hasattr(self, 'OtherClinicalTrialProtocolIDsSequence')
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Clinical Trial Subject Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult: Validation result object
        """
        return ClinicalTrialSubjectValidator.validate(self, config)
